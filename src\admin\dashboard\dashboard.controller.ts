import { Controller, Post, Put, Delete, Body, Param } from '@nestjs/common';
import { DashboardService } from './dashboard.service';

@Controller('admin/dashboard')
export class DashboardController {
  constructor(private readonly service: DashboardService) {}

  @Post('graphs')
  addGraph(@Body() graphData) {
    return this.service.addGraph(graphData);
  }

  @Put('graphs/:id')
  updateGraph(@Param('id') id: number, @Body() updateData) {
    return this.service.updateGraph(id, updateData);
  }

  @Delete('graphs/:id')
  deleteGraph(@Param('id') id: number) {
    return this.service.deleteGraph(id);
  }

  @Post('graphs/reorder')
  reorderGraphs(@Body() graphOrders) {
    return this.service.reorderGraphs(graphOrders);
  }

  @Post('filters')
  manageFilters(@Body() filterData) {
    return this.service.manageFilters(filterData);
  }
}
