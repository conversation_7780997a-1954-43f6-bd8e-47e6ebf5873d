import { Injectable } from '@nestjs/common';
import { PgService } from 'src/database/pg/pg.service';
import { GraphsEntity } from 'src/database/pg/entities/graphs.entity';
import { GraphFiltersEntity } from 'src/database/pg/entities/graphFilters.entity';

@Injectable()
export class DashboardService {
  constructor(private readonly pgService: PgService) {}

  // Add new graph
  async addGraph(graphData) {
    return await this.pgService.create(GraphsEntity, graphData);
  }

  // Update graph
  async updateGraph(id: number, updateData) {
    return await this.pgService.update(GraphsEntity, { id }, updateData);
  }

  // Delete graph
  async deleteGraph(id: number) {
    return await this.pgService.update(GraphsEntity, { id }, { isActive: false });
  }

  // Reorder graphs
  async reorderGraphs(graphOrders: Array<{ id: number; sortOrder: number }>) {
    const promises = graphOrders.map(({ id, sortOrder }) =>
      this.pgService.update(GraphsEntity, { id }, { sortOrder })
    );
    return await Promise.all(promises);
  }

  // Add/Update filters
  async manageFilters(filterData) {
    if (filterData.id) {
      return await this.pgService.update(GraphFiltersEntity, { id: filterData.id }, filterData);
    } else {
      return await this.pgService.create(GraphFiltersEntity, filterData);
    }
  }
}
