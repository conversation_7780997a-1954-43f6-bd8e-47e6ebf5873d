import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('graphs')
export class GraphsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  type: number; // 0=Bar, 1=Pie, 2=Box, 3=Line

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  subtitle: string;

  @Column()
  row: number;

  @Column()
  column: number;

  @Column()
  api: string;

  @Column()
  method: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  sortOrder: number;
}