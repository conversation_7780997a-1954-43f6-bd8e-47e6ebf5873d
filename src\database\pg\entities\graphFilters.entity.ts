import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('graphFilters')
export class GraphFiltersEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  type: number; // 1=Selection, 2=Range, 3=Date

  @Column()
  title: string;

  @Column()
  defaultValue: string;

  @Column('json', { nullable: true })
  otherInfo: any; // Options for dropdown, min/max for range

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  sortOrder: number;
}
